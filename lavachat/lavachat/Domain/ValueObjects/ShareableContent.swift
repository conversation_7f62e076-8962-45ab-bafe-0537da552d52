import Foundation
import UIKit

/// Protocol for entities that can be shared
protocol ShareableItem {
    /// Unique identifier for the item
    var id: UUID { get }
    
    /// The type of content this item represents
    var shareContentType: ShareContentType { get }
    
    /// Display name for the item
    var shareDisplayName: String { get }
    
    /// Convert the item to a shareable data structure
    func toShareableData() async throws -> ShareableData
    
    /// Validate that the item can be shared
    func validateForSharing() throws
}

/// Represents the complete shareable data package
struct ShareableData: Codable {
    let formatVersion: String
    let shareType: ShareContentType
    let exportedAt: Date
    let data: ShareableDataContent
    let metadata: ShareableMetadata
    
    init(
        shareType: ShareContentType,
        data: ShareableDataContent,
        metadata: ShareableMetadata? = nil
    ) {
        self.formatVersion = "1.0"
        self.shareType = shareType
        self.exportedAt = Date()
        self.data = data
        self.metadata = metadata ?? ShareableMetadata()
    }
}

/// Container for the actual shareable content
struct ShareableDataContent: Codable {
    let instance: LLMInstance?
    let model: LLMModel?
    let provider: LLMProvider?
    let chatSession: ChatSession?
    let messageAction: MessageAction?
    let messageActions: [MessageAction]? // For sharing multiple actions
    let chatSessionSetting: ChatSessionSetting?

    init(
        instance: LLMInstance? = nil,
        model: LLMModel? = nil,
        provider: LLMProvider? = nil,
        chatSession: ChatSession? = nil,
        messageAction: MessageAction? = nil,
        messageActions: [MessageAction]? = nil,
        chatSessionSetting: ChatSessionSetting? = nil
    ) {
        self.instance = instance
        self.model = model
        self.provider = provider
        self.chatSession = chatSession
        self.messageAction = messageAction
        self.messageActions = messageActions
        self.chatSessionSetting = chatSessionSetting
    }
}

/// Metadata about the sharing operation
struct ShareableMetadata: Codable {
    let appVersion: String
    let deviceInfo: String
    let exportedBy: String?
    let customData: [String: String]
    
    init(
        appVersion: String? = nil,
        deviceInfo: String? = nil,
        exportedBy: String? = nil,
        customData: [String: String] = [:]
    ) {
        self.appVersion = appVersion ?? Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        self.deviceInfo = deviceInfo ?? UIDevice.current.model
        self.exportedBy = exportedBy
        self.customData = customData
    }
}

/// Configuration for sharing operations
struct ShareConfiguration {
    let format: ShareFormat
    let includeMetadata: Bool
    let sanitizeData: Bool
    let customFileName: String?
    let icloudPermissions: ICloudSharePermissions?
    
    init(
        format: ShareFormat,
        includeMetadata: Bool = true,
        sanitizeData: Bool = true,
        customFileName: String? = nil,
        icloudPermissions: ICloudSharePermissions? = nil
    ) {
        self.format = format
        self.includeMetadata = includeMetadata
        self.sanitizeData = sanitizeData
        self.customFileName = customFileName
        self.icloudPermissions = icloudPermissions
    }
}

/// iCloud sharing permissions
struct ICloudSharePermissions: Codable {
    let allowsReadOnly: Bool
    let allowsReadWrite: Bool
    let expirationDate: Date?
    
    init(
        allowsReadOnly: Bool = true,
        allowsReadWrite: Bool = false,
        expirationDate: Date? = nil
    ) {
        self.allowsReadOnly = allowsReadOnly
        self.allowsReadWrite = allowsReadWrite
        self.expirationDate = expirationDate
    }
}

/// Configuration for import operations
struct ImportConfiguration {
    let allowDuplicates: Bool
    let resolveConflicts: Bool
    let createMissingDependencies: Bool
    let sanitizeImportedData: Bool
    
    init(
        allowDuplicates: Bool = false,
        resolveConflicts: Bool = true,
        createMissingDependencies: Bool = true,
        sanitizeImportedData: Bool = true
    ) {
        self.allowDuplicates = allowDuplicates
        self.resolveConflicts = resolveConflicts
        self.createMissingDependencies = createMissingDependencies
        self.sanitizeImportedData = sanitizeImportedData
    }
}

/// Represents a conflict that occurred during import
struct ImportConflict {
    let type: ConflictType
    let existingItemId: UUID
    let importedItemName: String
    let resolution: ConflictResolution
    
    enum ConflictType {
        case duplicateId
        case duplicateName
        case missingDependency
    }
    
    enum ConflictResolution {
        case skip
        case rename(String)
        case replace
        case createNew(UUID)
    }
}
