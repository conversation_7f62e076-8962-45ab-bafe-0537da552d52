import Foundation

/// Implementation of ImportServiceProtocol
class ImportServiceImpl: ImportServiceProtocol {
    
    // MARK: - Dependencies
    
    private let fileShareHandler: FileShareHandler
    private let cloudKitShareHandler: CloudKitShareHandler
    private let qrCodeShareHandler: QRCodeShareHandler
    private let llmRepository: LLMInstanceRepository
    private let chatRepository: ChatRepository
    
    // MARK: - State Management
    
    private var activeOperations: [UUID: ImportOperation] = [:]
    private let operationQueue = DispatchQueue(label: "com.lavachat.import", qos: .userInitiated)
    
    // MARK: - Initialization
    
    init(
        fileShareHandler: FileShareHandler,
        cloudKitShareHandler: CloudKitShareHandler,
        qrCodeShareHandler: QRCodeShareHandler,
        llmRepository: LLMInstanceRepository,
        chatRepository: ChatRepository
    ) {
        self.fileShareHandler = fileShareHandler
        self.cloudKitShareHandler = cloudKitShareHandler
        self.qrCodeShareHandler = qrCodeShareHandler
        self.llmRepository = llmRepository
        self.chatRepository = chatRepository
    }
    
    // MARK: - Core Import Methods
    
    func importFromFile(
        _ fileURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = ImportOperation(id: operationId, source: .file(fileURL), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        do {
            await updateProgress(operationId, progress: 0.1)
            
            // Read and parse the file
            let shareableData = try await fileShareHandler.readShareFile(from: fileURL)
            
            await updateProgress(operationId, progress: 0.3)
            
            // Process the import
            let result = try await processImport(shareableData, configuration: configuration, operationId: operationId)
            
            await updateProgress(operationId, progress: 1.0)
            return .success(result)
            
        } catch let error as ImportError {
            return .failure(error)
        } catch {
            return .failure(.unknownError(error.localizedDescription))
        }
    }
    
    func importFromICloudShare(
        _ shareURL: URL,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        let operationId = UUID()
        let operation = ImportOperation(id: operationId, source: .icloud(shareURL), configuration: configuration)
        
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId] = operation
                continuation.resume()
            }
        }
        
        defer {
            Task {
                await withCheckedContinuation { continuation in
                    operationQueue.async {
                        self.activeOperations.removeValue(forKey: operationId)
                        continuation.resume()
                    }
                }
            }
        }
        
        do {
            await updateProgress(operationId, progress: 0.1)
            
            // Accept and fetch the iCloud share
            let shareableData = try await cloudKitShareHandler.acceptICloudShare(shareURL)
            
            await updateProgress(operationId, progress: 0.3)
            
            // Process the import
            let result = try await processImport(shareableData, configuration: configuration, operationId: operationId)
            
            await updateProgress(operationId, progress: 1.0)
            return .success(result)
            
        } catch let error as ImportError {
            return .failure(error)
        } catch {
            return .failure(.unknownError(error.localizedDescription))
        }
    }
    
    func importFromQRCode(
        _ qrCodeData: String,
        configuration: ImportConfiguration
    ) async -> ImportResult {
        // Validate QR code data
        guard qrCodeShareHandler.isValidLavaChatShareURL(qrCodeData) else {
            return .failure(.invalidFileFormat)
        }
        
        // If it's an iCloud URL, delegate to iCloud import
        if let url = URL(string: qrCodeData), url.host?.contains("icloud.com") == true {
            return await importFromICloudShare(url, configuration: configuration)
        }
        
        // Handle other custom URL schemes if needed
        return .failure(.unsupportedVersion("QR code format not supported"))
    }
    
    // MARK: - Validation and Preview
    
    func canImportFile(_ fileURL: URL) async -> Bool {
        return await fileShareHandler.canImportFile(fileURL)
    }
    
    func getImportPreview(_ fileURL: URL) async -> ImportPreview? {
        return await fileShareHandler.getFilePreview(fileURL)
    }
    
    func canImportFromQRCode(_ qrCodeData: String) async -> Bool {
        return qrCodeShareHandler.isValidLavaChatShareURL(qrCodeData)
    }
    
    // MARK: - Conflict Resolution
    
    func resolveConflicts(
        _ conflicts: [ImportConflict],
        resolver: @escaping (ImportConflict) async -> ImportConflict.ConflictResolution
    ) async -> [ImportConflict] {
        var resolvedConflicts: [ImportConflict] = []
        
        for conflict in conflicts {
            let resolution = await resolver(conflict)
            let resolvedConflict = ImportConflict(
                type: conflict.type,
                existingItemId: conflict.existingItemId,
                importedItemName: conflict.importedItemName,
                resolution: resolution
            )
            resolvedConflicts.append(resolvedConflict)
        }
        
        return resolvedConflicts
    }
    
    // MARK: - Progress and Cancellation
    
    func cancelImportOperation(_ operationId: UUID) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                if let operation = self.activeOperations[operationId] {
                    operation.isCancelled = true
                }
                continuation.resume()
            }
        }
    }
    
    func getImportProgress(_ operationId: UUID) -> Double? {
        return activeOperations[operationId]?.progress
    }
    
    // MARK: - Private Helper Methods
    
    private func updateProgress(_ operationId: UUID, progress: Double) async {
        await withCheckedContinuation { continuation in
            operationQueue.async {
                self.activeOperations[operationId]?.progress = progress
                continuation.resume()
            }
        }
    }
    
    private func processImport(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration,
        operationId: UUID
    ) async throws -> ImportSuccessInfo {
        // Validate format version
        guard shareableData.formatVersion == "1.0" else {
            throw ImportError.unsupportedVersion(shareableData.formatVersion)
        }
        
        var importedItemIds: [UUID] = []
        var conflictsResolved: [String] = []
        
        await updateProgress(operationId, progress: 0.4)
        
        // Process based on content type
        switch shareableData.shareType {
        case .llmInstance:
            let result = try await importLLMInstance(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
            
        case .chatSession:
            let result = try await importChatSession(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
            
        case .messageAction:
            let result = try await importMessageAction(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)

        case .chatSessionSetting:
            let result = try await importChatSessionSetting(shareableData, configuration: configuration)
            importedItemIds.append(contentsOf: result.itemIds)
            conflictsResolved.append(contentsOf: result.conflicts)
        }
        
        await updateProgress(operationId, progress: 0.9)
        
        return ImportSuccessInfo(
            contentType: shareableData.shareType,
            importedItemIds: importedItemIds,
            conflictsResolved: conflictsResolved,
            metadata: ["formatVersion": shareableData.formatVersion]
        )
    }
    
    private func importLLMInstance(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String]) {
        guard let instance = shareableData.data.instance else {
            throw ImportError.missingRequiredFields("LLM Instance")
        }
        
        var importedIds: [UUID] = []
        var conflicts: [String] = []
        
        // Import provider if included
        if let provider = shareableData.data.provider {
            let existingProvider = try await llmRepository.getProvider(byId: provider.id)
            if existingProvider == nil || configuration.allowDuplicates {
                let importProvider: LLMProvider
                if existingProvider != nil && configuration.resolveConflicts {
                    // Create new provider with new ID
                    importProvider = LLMProvider(
                        id: UUID(),
                        name: provider.name,
                        logoImageName: provider.logoImageName,
                        customLogoData: provider.customLogoData,
                        websiteUrl: provider.websiteUrl,
                        apiDocumentationUrl: provider.apiDocumentationUrl,
                        apiBaseUrl: provider.apiBaseUrl,
                        providerType: provider.providerType,
                        apiKeyStored: false, // Never import API keys
                        apiStyle: provider.apiStyle,
                        apiEndpointPath: provider.apiEndpointPath,
                        isUserCreated: provider.isUserCreated,
                        isUserModified: provider.isUserModified,
                        metadata: provider.metadata
                    )
                    conflicts.append("Provider '\(provider.name)' renamed due to conflict")
                } else {
                    importProvider = provider
                }
                try await llmRepository.createProvider(importProvider)
                importedIds.append(importProvider.id)
            }
        }
        
        // Import model if included
        if let model = shareableData.data.model {
            let existingModel = try await llmRepository.getModel(byId: model.id)
            if existingModel == nil || configuration.allowDuplicates {
                let importModel: LLMModel
                if existingModel != nil && configuration.resolveConflicts {
                    // Create new model with new ID
                    importModel = LLMModel(
                        id: UUID(),
                        providerId: model.providerId,
                        modelIdentifier: model.modelIdentifier,
                        name: model.name,
                        modelDescription: model.modelDescription,
                        logoImageName: model.logoImageName,
                        customLogoData: model.customLogoData,
                        contextWindowSize: model.contextWindowSize,
                        inputModalities: model.inputModalities,
                        outputModalities: model.outputModalities,
                        thinkingCapabilities: model.thinkingCapabilities,
                        searchingCapabilities: model.searchingCapabilities,
                        maxOutputTokens: model.maxOutputTokens,
                        pricingInfo: model.pricingInfo,
                        group: model.group,
                        availabilityStatus: model.availabilityStatus,
                        isDefaultRecommendation: model.isDefaultRecommendation,
                        isUserCreated: model.isUserCreated,
                        isUserModified: model.isUserModified,
                        apiConfigsOverride: model.apiConfigsOverride,
                        metadata: model.metadata
                    )
                    conflicts.append("Model '\(model.name)' renamed due to conflict")
                } else {
                    importModel = model
                }
                try await llmRepository.createModel(importModel)
                importedIds.append(importModel.id)
            }
        }
        
        // Import instance
        let existingInstance = try await llmRepository.getInstance(byId: instance.id)
        if existingInstance == nil || configuration.allowDuplicates {
            let importInstance: LLMInstance
            if existingInstance != nil && configuration.resolveConflicts {
                // Create new instance with new ID and modified name
                importInstance = LLMInstance(
                    id: UUID(),
                    modelId: instance.modelId,
                    name: "\(instance.name) (Imported)",
                    customLogoData: instance.customLogoData,
                    systemPrompt: instance.systemPrompt,
                    defaultParameters: instance.defaultParameters,
                    totalPromptTokensUsed: 0, // Reset usage stats
                    totalCompletionTokensUsed: 0,
                    createdAt: Date(),
                    lastUsedAt: nil,
                    isFavorited: false, // Reset favorite status
                    isUserModified: instance.isUserModified,
                    metadata: instance.metadata
                )
                conflicts.append("Instance '\(instance.name)' renamed due to conflict")
            } else {
                importInstance = instance
            }
            try await llmRepository.createInstance(importInstance)
            importedIds.append(importInstance.id)
        }
        
        return (itemIds: importedIds, conflicts: conflicts)
    }
    
    private func importChatSession(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String]) {
        guard let session = shareableData.data.chatSession else {
            throw ImportError.missingRequiredFields("Chat Session")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []

        // Import related message actions first
        if let messageActions = shareableData.data.messageActions {
            let actionResult = try await importMessageActions(messageActions, configuration: configuration)
            importedIds.append(contentsOf: actionResult.itemIds)
            conflicts.append(contentsOf: actionResult.conflicts)
        }

        // Import chat session setting if included
        if let setting = shareableData.data.chatSessionSetting {
            let settingResult = try await importChatSessionSettingInternal(setting, configuration: configuration)
            importedIds.append(contentsOf: settingResult.itemIds)
            conflicts.append(contentsOf: settingResult.conflicts)
        }

        // Import the chat session itself
        let existingSession = try await chatRepository.getChatSession(byId: session.id)
        if existingSession == nil || configuration.allowDuplicates {
            let importSession: ChatSession
            if existingSession != nil && configuration.resolveConflicts {
                // Create new session with new ID and modified title
                importSession = ChatSession(
                    id: UUID(),
                    title: "\(session.title) (Imported)",
                    createdAt: Date(),
                    lastModifiedAt: Date(),
                    activeMessageId: nil, // Reset active message
                    activeLLMInstanceIds: session.activeLLMInstanceIds,
                    usedLLMInstanceIds: session.usedLLMInstanceIds,
                    activeContextServerIds: session.activeContextServerIds,
                    settingsId: session.settingsId,
                    userId: session.userId,
                    instanceSettings: session.instanceSettings,
                    metadata: session.metadata
                )
                conflicts.append("Session '\(session.title)' renamed due to conflict")
            } else {
                importSession = session
            }
            try await chatRepository.createChatSession(importSession)
            importedIds.append(importSession.id)
        }

        return (itemIds: importedIds, conflicts: conflicts)
    }
    
    private func importMessageAction(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String]) {
        guard let action = shareableData.data.messageAction else {
            throw ImportError.missingRequiredFields("Message Action")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []

        // Skip system actions - they should not be imported
        if case .system = action.actionType {
            return (itemIds: importedIds, conflicts: conflicts)
        }

        let existingAction = try await chatRepository.getMessageAction(byId: action.id)
        if existingAction == nil || configuration.allowDuplicates {
            let importAction: MessageAction
            if existingAction != nil && configuration.resolveConflicts {
                // Create new action with new ID and modified name
                importAction = MessageAction(
                    id: UUID(),
                    name: "\(action.name) (Imported)",
                    icon: action.icon,
                    actionType: action.actionType,
                    prompts: action.prompts,
                    targetLLMInstanceId: action.targetLLMInstanceId,
                    metadata: action.metadata
                )
                conflicts.append("Action '\(action.name)' renamed due to conflict")
            } else {
                importAction = action
            }
            try await chatRepository.createMessageAction(importAction)
            importedIds.append(importAction.id)
        }

        return (itemIds: importedIds, conflicts: conflicts)
    }
    
    private func importChatSessionSetting(
        _ shareableData: ShareableData,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String]) {
        guard let setting = shareableData.data.chatSessionSetting else {
            throw ImportError.missingRequiredFields("Chat Session Setting")
        }

        var importedIds: [UUID] = []
        var conflicts: [String] = []

        // Import related message actions first
        if let messageActions = shareableData.data.messageActions {
            let actionResult = try await importMessageActions(messageActions, configuration: configuration)
            importedIds.append(contentsOf: actionResult.itemIds)
            conflicts.append(contentsOf: actionResult.conflicts)
        }

        // Import the chat session setting
        let settingResult = try await importChatSessionSettingInternal(setting, configuration: configuration)
        importedIds.append(contentsOf: settingResult.itemIds)
        conflicts.append(contentsOf: settingResult.conflicts)

        return (itemIds: importedIds, conflicts: conflicts)
    }

    // MARK: - Helper Methods for MessageAction Import

    private func importMessageActions(
        _ messageActions: [MessageAction],
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String]) {
        var importedIds: [UUID] = []
        var conflicts: [String] = []

        for action in messageActions {
            // Skip system actions - they should not be imported
            if case .system = action.actionType {
                continue
            }

            let existingAction = try await chatRepository.getMessageAction(byId: action.id)
            if existingAction == nil || configuration.allowDuplicates {
                let importAction: MessageAction
                if existingAction != nil && configuration.resolveConflicts {
                    // Create new action with new ID
                    importAction = MessageAction(
                        id: UUID(),
                        name: "\(action.name) (Imported)",
                        icon: action.icon,
                        actionType: action.actionType,
                        prompts: action.prompts,
                        targetLLMInstanceId: action.targetLLMInstanceId,
                        metadata: action.metadata
                    )
                    conflicts.append("Action '\(action.name)' renamed due to conflict")
                } else {
                    importAction = action
                }
                try await chatRepository.createMessageAction(importAction)
                importedIds.append(importAction.id)
            }
        }

        return (itemIds: importedIds, conflicts: conflicts)
    }

    private func importChatSessionSettingInternal(
        _ setting: ChatSessionSetting,
        configuration: ImportConfiguration
    ) async throws -> (itemIds: [UUID], conflicts: [String]) {
        var importedIds: [UUID] = []
        var conflicts: [String] = []

        let existingSetting = try await chatRepository.getSetting(byId: setting.id)
        if existingSetting == nil || configuration.allowDuplicates {
            let importSetting: ChatSessionSetting
            if existingSetting != nil && configuration.resolveConflicts {
                // Create new setting with new ID and modified name
                importSetting = ChatSessionSetting(
                    id: UUID(),
                    name: "\(setting.name) (Imported)",
                    isSystemDefault: false, // Never import as system default
                    createdAt: Date(),
                    lastModifiedAt: Date(),
                    llmParameterOverrides: setting.llmParameterOverrides,
                    defaultContextServerIds: setting.defaultContextServerIds,
                    shouldExpandThinking: setting.shouldExpandThinking,
                    uiThemeSettings: setting.uiThemeSettings,
                    messageActionSettings: setting.messageActionSettings,
                    savedPromptSegments: setting.savedPromptSegments,
                    auxiliaryLLMInstanceId: setting.auxiliaryLLMInstanceId,
                    shouldAutoGenerateTitle: setting.shouldAutoGenerateTitle,
                    contextMessageCount: setting.contextMessageCount,
                    metadata: setting.metadata
                )
                conflicts.append("Setting '\(setting.name)' renamed due to conflict")
            } else {
                importSetting = setting
            }
            try await chatRepository.createSetting(importSetting)
            importedIds.append(importSetting.id)
        }

        return (itemIds: importedIds, conflicts: conflicts)
    }
}

// MARK: - Supporting Types

private class ImportOperation {
    let id: UUID
    let source: ImportSource
    let configuration: ImportConfiguration
    var progress: Double = 0.0
    var isCancelled: Bool = false
    
    init(id: UUID, source: ImportSource, configuration: ImportConfiguration) {
        self.id = id
        self.source = source
        self.configuration = configuration
    }
}

private enum ImportSource {
    case file(URL)
    case icloud(URL)
    case qrCode(String)
}
